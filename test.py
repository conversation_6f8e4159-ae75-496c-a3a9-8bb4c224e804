import os
import io
from PIL import Image
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

# Set Gemini API key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCJaADIHnUd3TmZDfyeh2JKk_k8WO6t7JI"

# === Replace with your local image file paths ===
example_image_path = "/content/screencapture-notion-2025-06-17-14_48_55.png"
target_image_path = "/content/screencapture-linear-app-2025-06-17-14_49_57.png"

# Load and convert local images to byte streams
def image_to_bytes(image_path):
    image = Image.open(image_path).convert("RGB")
    byte_stream = io.BytesIO()
    image.save(byte_stream, format="PNG")
    return byte_stream.getvalue()

example_image_bytes = image_to_bytes(example_image_path)
target_image_bytes = image_to_bytes(target_image_path)

# URL values (text only)
example_url = "https://www.notion.com"
target_url = "https://linear.app"

# Example output to guide the model
# Example output to guide the model
example_output = """
Run a UX audit for this website: https://www.notion.com
SCREENSHOT: "notion.png given with this prompt"
Please evaluate using Nielsen’s heuristics and general UX copywriting standards.
UX Audit Report for Notion
Status: In Progress
Observations:
1. Location: Home page - Top nav/Hero section
Severity: High
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
Information architecture
UX copy
Observation:
The copy in the top strip, “All-in-one AI, right where you work” is vague and doesn’t provide enough context.
It competes with navigation due to visual prominence.
The link 'Try Notion AI free' leads to a repetitive CTA experience, causing confusion and pressure.
2. Location: Home page - Nav bar
Severity: High
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
Information architecture
UX copy
Observation:
The navigation label 'Notion' adds to CTA overload and makes the user experience feel rushed.
3. Location: Landing Page - Top nav
Severity: Medium
Heuristics Violated:
H7: Flexibility and efficiency of use
H12: Pleasurable and respectful interaction with the user
H8: Aesthetic and minimalist design
Information architecture
Observation:
The top navbar has disorganized options (AI, enterprises, pricing) that don’t align with product features.
AI appears both in the nav and as a CTA, reducing clarity and consistency.
4. Location: Landing Page - Hero section
Severity: Medium
Heuristics Violated:
H7: Flexibility and efficiency of use
H6: Recognition rather than recall
H8: Aesthetic and minimalist design
Observation:
The heading and subtext are vague, reduce the value proposition, and are difficult for users to relate to.
5. Location: Landing page - Hero section
Severity: Low
Heuristics Violated:
H5: Error prevention
H9: Help users recognize, diagnose, and recover from errors
Observation:
The banner image is uncommunicative, unrelated to the product, takes up excessive space, and adds usability friction.
6. Location: Landing page - AI meeting notes card groups
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H6: Recognition rather than recall
H12: Pleasurable and respectful interaction with the user
H8: Aesthetic and minimalist design
Observation:
The tooltip adds unnecessary friction.
The heading and subheading visually compete due to icon and badge placement.
Arrow placement is inconsistent across vertical and horizontal cards.
There are low contrast issues in the 'new' badge and Notion Mail card.
7. Location: Landing page - One space for everything
Severity: Low
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
H10: Help and documentation
H6: Recognition rather than recall
Observation:
Screenshots under 'one space for every team' don't align with navigation options and add cognitive load.
8. Location: Landing page - Customer stories
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H6: Recognition rather than recall
H12: Pleasurable and respectful interaction with the user
Information Architecture
Observation:
5 out of 7 testimonials have quoted subtexts, while 2 do not.
One testimonial is larger than the others.
Too much reading content distracts from the section's usefulness.
9. Location: Landing page - Find anything with one search
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H12: Pleasurable and respectful interaction with the user
Observation:
The section appears visually bland due to low contrast and breaks the visual hierarchy.
10. Location: Landing page - Get started on Notion
Severity: Low
Heuristics Violated:
H6: Recognition rather than recall
H4: Consistency and standards
H5: Error prevention
Observation:
There are inconsistent CTA labels—one is Mac-specific vs. a generic 'download'.
The same color is used across different cards, breaking visual group distinction.
"""

# Instruction prompt with embedded example
instruction_text = f"""
You are an elite, meticulous UX evaluator, specializing in highly detailed, evidence-based heuristic audits of marketing SaaS landing pages. Your task is to perform a comprehensive heuristic evaluation based EXCLUSIVELY on the static screenshot provided.
Your analysis must be derived ONLY from the visual cues, structure, text, and layout elements that are explicitly and undeniably VISIBLE in the screenshot.
You are NOT allowed to make assumptions about interactivity, animations, hover states, scrolling behavior, or any hidden UI elements. Everything must be treated as frozen in its presented state.

<system_constraints>
  You are operating in a constrained UX audit environment. You are an elite, meticulous UX evaluator whose role is to conduct high-fidelity heuristic evaluations of SaaS landing pages. All evaluations are based EXCLUSIVELY on the visual appearance of a **static screenshot**.

  HARD CONSTRAINTS:
  - You MUST NOT infer functionality, interactivity, or behavior (e.g., hover states, dynamic elements, hidden states, scroll behavior).
  - You MUST NOT generate assumptions about the product, purpose, or features beyond what is VISIBLE in the screenshot.
  - You MUST NOT hallucinate observations or extrapolate meanings not DIRECTLY visible from the screenshot content.
  - You MUST NOT offer solutions or redesign suggestions — focus SOLELY on observation and negative user impact.
  - You MAY reference heuristics (e.g., Nielsen’s) and UX principles ONLY if there is a direct and visually traceable violation.

  PERMITTED EVIDENCE SOURCES:
  - PRIMARY: The static screenshot provided by the user. All observations MUST be visually grounded in this source.
  - SUPPLEMENTARY: A provided URL may be crawled (static GET only) to inspect **only the first-load** HTML, CSS, and visible text DOM. JavaScript-rendered states, delayed content, A/B tests, auth walls, or animations must be treated as non-existent unless visible in the static screenshot.

  GOAL:
  Your goal is to identify clear, visible, significant UX or UI usability issues based on visual hierarchy, consistency, alignment, spacing, text clarity, accessibility, and other visually confirmable cues. You must mirror the granularity, tone, and specificity of expert human-led UX audits.

  PROCESS:
  - Scan from top to bottom of the screenshot in a systematic, section-by-section flow.
  - Within each section, inspect each individual element (text, button, image, badge, CTA, icon, card, etc.) and their group consistency.
  - Compare similar elements for visual inconsistencies.
  - For each visually evident issue, document:
    - Exact Location (e.g., “Hero Section – CTA Button”)
    - Violated UX Heuristics (e.g., Consistency and Standards, Visual Hierarchy)
    - Severity (Low, Medium, High)
    - Observation (specific, visual-evidence-based, 4–6 lines minimum, no assumptions)

  OUTPUT FORMAT:
  Your output MUST be a Markdown table with the following columns:
    * id: Unique identifier for the observation.
    * location: Provide the most granular and precise name for the specific UI element or visual region where the issue is undeniably visible. This must pinpoint a single, distinct visual component, or a specific part of a component.
        * Examples: "Header - 'Sign in' link", "Nav bar - 'Notion' link", "Hero Section - Main Heading Text", "Hero Section - Primary CTA Button ('Get Notion free' in header)", "Hero Section - Main Illustration/Image", "AI meeting notes card group - Vertical card 1 ('AI meeting notes')", "AI meeting notes card group - Horizontal card ('Notion Mail')", "AI meeting notes card group - 'New' badge on 'AI meeting notes' card", "Customer stories section - Testimonial Quote 1", "Footer - 'Product' links column", "Section 'Find anything with one search' - Heading", "Section 'get started on Notion' - 'Download for Mac' card".
    * heuristics_violated: An array of relevant UX principles/heuristics (from the comprehensive list above) that are violated by this specific UI element/issue.
    * severity: Assign "Low", "Medium", or "High" based on the visual impact and potential disruption to user experience.
    * observation: This must be a single, detailed narrative string. Within this string, you must:
    * Clearly describe the visually evident and specific issue related to the location.
    * Quote any exact visible text or alt-text relevant to this specific issue.
    * Mention if a CSS selector / XPath was found (otherwise state "N/A").
    * Crucially, explain how this specific visual characteristic or design choice, pertaining only to this exact UI element, could negatively impact the user's understanding, cognitive load, navigation, engagement, or overall experience. Be analytical and directly link the visual observation to the violated principles and user impact.
    * STRICTLY adhere to the visual evidence from the screenshot. DO NOT infer or invent.
    * DO NOT generalize or connect to other UI elements or sections if the issue is not directly and visually manifested by this specific element. If an identical visual issue appears on multiple distinct UI elements, create a separate observation for each instance, ensuring each location is unique to that element.

  RULES:
  - DO NOT combine issues across sections.
  - DO NOT reference functionality or flows not visible in the screenshot.
  - DO NOT provide design suggestions or enhancements.
  - DO NOT generalize observations — one row per specific issue.
  - DO NOT use vague language like “might confuse” — state the precise visual evidence and its user impact.

  Your behavior should emulate a senior human UX researcher performing a pixel-perfect visual audit.

</system_constraints>

[10-rule Markdown or bullet list]

✅ 10 Rigorous Heuristic Evaluation Rules (Markdown Table)
ID      Heuristic Rule (for Model Use)


H1      All observations must be based solely on what is visually and undeniably present in the screenshot. No assumptions about interactivity, hover states, or off-screen behavior are permitted.
Example:JSON:-
{{
  "heuristic": "H1",
  "location": "Top Navigation Bar",
  "observation": "There is a hamburger icon on the top-left, but no label or visible text indicating what it opens. Since the contents of the menu are not shown in the screenshot, no assumptions are made about its interactivity.",
  "severity": "Low"
}}

H2      Do not merge observations across sections or components. Each issue must be tied to one specific, visibly distinct element or immediate visual context.
Example:-
{{
  "heuristic": "H2",
  "location": "Feature Section > Card 2",
  "observation": "The icon in Card 2 is 5–6px lower than those in Card 1 and Card 3, breaking alignment within the card group. This issue is specific to Card 2 only.",
  "severity": "Medium"
}}

H3      Scrutinize micro-consistency within grouped elements. Actively compare spacing, alignment, icon positioning, or visual format across cards, CTAs, or testimonials to detect subtle inconsistencies.
Example:-
{{
  "heuristic": "H3",
  "location": "Testimonials Section",
  "observation": "Among three testimonial cards, Card 3 has an extra 10px of top margin on the quote text compared to the others, breaking visual rhythm.",
  "severity": "Medium"
}}

H4      Flag any imbalance or disruption in visual hierarchy. Identify when less important elements visually overpower or compete with key content like the hero heading or main CTA.
Example:-{{
  "heuristic": "H4",
  "location": "Hero Section",
  "observation": "The secondary CTA 'Learn More' uses a high-saturation blue and larger font size than the primary CTA 'Get Started', drawing disproportionate attention.",
  "severity": "High"
}}
H5      Evaluate imagery/animation for communication efficiency. Flag any visible visuals that take up significant screen space but fail to support product comprehension or context.
Example:-{{
  "heuristic": "H5",
  "location": "Hero Banner Background",
  "observation": "A large abstract video loop occupies nearly half the viewport without providing any context or supporting product explanation. It adds visual noise rather than clarity.",
  "severity": "Medium"
}}
H6      Assess clarity and specificity of all visible microcopy. Highlight vague or generic headings, subtexts, or CTAs that fail to communicate product value or purpose clearly.
Example:-{{
  "heuristic": "H6",
  "location": "Hero Section > Main Heading & Subtext",
  "observation": "The heading reads 'Unlock Your Potential' and the subtext says 'Do more with less', both of which are vague and do not explain the product, service, or offering in any concrete way.",
  "severity": "High"
}}
H7      Check for visual contrast and accessibility violations. Mark any text or badges that suffer from low contrast or weak visibility against background colors.
Example:-{{
  "heuristic": "H7",
  "location": "Pricing Section > Plan Card 3",
  "observation": "The white plan title text is placed over a light gray background with low contrast, making it difficult to read, especially for visually impaired users.",
  "severity": "High"
}}
H8      Detect visual rhythm or harmony breaks. Identify if changes in spacing, weight, or layout density create jarring transitions or disrupt the flow of the visual experience.
Example:-{{
  "heuristic": "H8",
  "location": "Feature Section > Card Layout",
  "observation": "The first two feature cards maintain consistent spacing, but the third card is placed 30px lower, creating a jagged, uneven row. This disrupts the visual flow.",
  "severity": "Medium"
}}
H9      Do not invent CSS selectors or metadata if not visible. If a selector or XPath isn't found directly via DOM crawl or visible inspection, state it as “N/A”.
Example:-{{
  "heuristic": "H9",
  "location": "CTA Button in Header",
  "observation": "The button shows the label 'Join Now', but no class name or selector is visible in the screenshot. Therefore, the selector is marked as 'N/A'.",
  "severity": "Low"
}}
H10     No recommendations. Only document precise visual problems and their impact. Do not suggest fixes or improvements; strictly record and analyze observed issues.{{
  "heuristic": "H10",
  "location": "Onboarding Modal",
  "observation": "The modal appears visually dense with four equal-weighted buttons and three text blocks stacked tightly. This creates a cluttered appearance with unclear hierarchy.",
  "severity": "Medium"
}}

Final Validation Check:
Before including any observation, ask: "Would fixing this issue genuinely improve user experience, or am I flagging a design choice preference?"

### Output Format:
{{
  "audit_report": {{
    "title": "<Title>",
    "observations": [
      {{
        "id": <int>,
        "location": "<Page location>",
        "severity": "<Low/Medium/High>",
        "section": "<Section name>",
        "heuristics_violated": ["<Heuristic 1>", "<Heuristic 2>"],
        "observation": "<Detailed observation>"
      }}
    ]
  }}
}}

### Example:
- Screenshot: [attached]
- URL: {example_url}
- Expected Output:
{example_output}

---

Now audit the following input:
- Screenshot: [attached below]
- URL: {target_url}
"""

# Initialize Gemini model
llm = ChatGoogleGenerativeAI(model="gemini-2.5-flash-preview-05-20", temperature=0.3)

# LangChain-compatible message sequence
messages = [
    SystemMessage(content="You are an expert UX auditor."),
    HumanMessage(
        content=[
            {"type": "text", "text": instruction_text},
            {"type": "media", "data": example_image_bytes, "mime_type": "image/png"},
            {"type": "text", "text": f"Audit this new input:\nScreenshot: [attached below]\nURL: {target_url}"},
            {"type": "media", "data": target_image_bytes, "mime_type": "image/png"}
        ]
    )
]

# Run the model
response = llm.invoke(messages)

# Output
print(response.content)
