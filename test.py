import os
import io
import requests
from PIL import Image
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from bs4 import BeautifulSoup
import json
from urllib.parse import urljoin, urlparse
import time

"""
Enhanced UX Audit Script with Image and URL Analysis
====================================================

This script performs comprehensive UX audits by analyzing both:
1. Visual design from screenshots (primary analysis)
2. Website structure from URL scraping (secondary analysis)

Features:
- Dual-source analysis (visual + structural)
- Accessibility evaluation
- Content hierarchy assessment
- Technical implementation review
- Detailed heuristic evaluation

Requirements:
- Valid Gemini API key
- Screenshot images of the websites
- Internet connection for URL scraping
"""

# ============================================================================
# CONFIGURATION SECTION - UPDATE THESE VALUES
# ============================================================================

# Set Gemini API key (replace with your actual API key)
os.environ["GOOGLE_API_KEY"] = "AIzaSyCJaADIHnUd3TmZDfyeh2JKk_k8WO6t7JI"

# === UPDATE THESE PATHS TO YOUR LOCAL IMAGE FILES ===
example_image_path = "/content/screencapture-notion-2025-06-17-14_48_55.png"
target_image_path = "/content/screencapture-linear-app-2025-06-17-14_49_57.png"

# === UPDATE THESE URLs FOR THE WEBSITES TO ANALYZE ===
example_url = "https://www.notion.com"
target_url = "https://linear.app"

# Model configuration
MODEL_NAME = "gemini-2.5-flash-preview-05-20"
MODEL_TEMPERATURE = 0.3

# ============================================================================

def validate_configuration():
    """
    Validate that all required configuration is properly set
    """
    issues = []

    # Check API key
    if not os.environ.get("GOOGLE_API_KEY") or os.environ.get("GOOGLE_API_KEY") == "your_api_key_here":
        issues.append("Please set a valid GOOGLE_API_KEY in the configuration section")

    # Check image paths
    if not os.path.exists(target_image_path):
        issues.append(f"Target image not found: {target_image_path}")

    if not os.path.exists(example_image_path):
        issues.append(f"Example image not found: {example_image_path} (will continue without example)")

    # Check URLs
    if not target_url.startswith(('http://', 'https://')):
        issues.append(f"Invalid target URL format: {target_url}")

    if not example_url.startswith(('http://', 'https://')):
        issues.append(f"Invalid example URL format: {example_url}")

    return issues

def save_audit_results(results, filename=None):
    """
    Save audit results to a file
    """
    if not filename:
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        filename = f"ux_audit_{timestamp}.txt"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"Enhanced UX Audit Results\n")
            f.write(f"Target URL: {target_url}\n")
            f.write(f"Analysis Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Model Used: {MODEL_NAME}\n")
            f.write("="*80 + "\n\n")
            f.write(results)

        print(f"\nAudit results saved to: {filename}")
        return filename
    except Exception as e:
        print(f"Error saving results: {e}")
        return None

# Enhanced web scraping function
def scrape_website_content(url, max_retries=3):
    """
    Scrape website content including HTML structure, meta tags, and visible text.
    Returns structured data about the website for UX analysis.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract key website information
            website_data = {
                'url': url,
                'title': soup.find('title').get_text(strip=True) if soup.find('title') else 'No title found',
                'meta_description': '',
                'h1_tags': [h1.get_text(strip=True) for h1 in soup.find_all('h1')],
                'h2_tags': [h2.get_text(strip=True) for h2 in soup.find_all('h2')],
                'navigation_links': [],
                'cta_buttons': [],
                'form_elements': [],
                'images_alt_text': [],
                'page_structure': '',
                'visible_text_sample': ''
            }

            # Meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                website_data['meta_description'] = meta_desc.get('content', '')

            # Navigation links
            nav_elements = soup.find_all(['nav', 'header'])
            for nav in nav_elements:
                links = nav.find_all('a')
                for link in links:
                    text = link.get_text(strip=True)
                    href = link.get('href', '')
                    if text and len(text) < 50:  # Filter out very long text
                        website_data['navigation_links'].append({'text': text, 'href': href})

            # CTA buttons and links
            buttons = soup.find_all(['button', 'a'])
            for button in buttons:
                text = button.get_text(strip=True)
                classes = ' '.join(button.get('class', []))
                if any(keyword in classes.lower() or keyword in text.lower()
                      for keyword in ['cta', 'button', 'btn', 'sign', 'get', 'start', 'try', 'download']):
                    website_data['cta_buttons'].append({'text': text, 'classes': classes})

            # Form elements
            forms = soup.find_all('form')
            for form in forms:
                inputs = form.find_all(['input', 'textarea', 'select'])
                form_data = []
                for inp in inputs:
                    form_data.append({
                        'type': inp.get('type', inp.name),
                        'placeholder': inp.get('placeholder', ''),
                        'name': inp.get('name', '')
                    })
                website_data['form_elements'].append(form_data)

            # Images with alt text
            images = soup.find_all('img')
            for img in images:
                alt_text = img.get('alt', '')
                src = img.get('src', '')
                if alt_text:
                    website_data['images_alt_text'].append({'alt': alt_text, 'src': src})

            # Page structure overview
            main_sections = soup.find_all(['main', 'section', 'article', 'div'])
            structure_info = []
            for section in main_sections[:10]:  # Limit to first 10 sections
                classes = ' '.join(section.get('class', []))
                if classes:
                    structure_info.append(f"Section with classes: {classes}")
            website_data['page_structure'] = '; '.join(structure_info)

            # Sample of visible text (first 500 chars)
            visible_text = soup.get_text(separator=' ', strip=True)
            website_data['visible_text_sample'] = visible_text[:500] + '...' if len(visible_text) > 500 else visible_text

            return website_data

        except requests.RequestException as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
            else:
                return {
                    'url': url,
                    'error': f"Failed to scrape after {max_retries} attempts: {str(e)}",
                    'title': 'Error loading page',
                    'meta_description': '',
                    'h1_tags': [],
                    'h2_tags': [],
                    'navigation_links': [],
                    'cta_buttons': [],
                    'form_elements': [],
                    'images_alt_text': [],
                    'page_structure': '',
                    'visible_text_sample': ''
                }

# Load and convert local images to byte streams
def image_to_bytes(image_path):
    """Convert local image file to bytes for model input"""
    try:
        image = Image.open(image_path).convert("RGB")
        byte_stream = io.BytesIO()
        image.save(byte_stream, format="PNG")
        return byte_stream.getvalue()
    except Exception as e:
        print(f"Error loading image {image_path}: {e}")
        return None

# Load images with error handling
print("Loading images...")
example_image_bytes = image_to_bytes(example_image_path)
target_image_bytes = image_to_bytes(target_image_path)

# Check if images loaded successfully
if example_image_bytes is None:
    print(f"Warning: Could not load example image from {example_image_path}")
if target_image_bytes is None:
    print(f"Warning: Could not load target image from {target_image_path}")
    print("Script cannot continue without target image. Please check the file path.")
    exit(1)

# Scrape website content for both example and target
print("Scraping example website content...")
example_website_data = scrape_website_content(example_url)
print(f"Example website data collected: {len(example_website_data)} fields")

print("Scraping target website content...")
target_website_data = scrape_website_content(target_url)
print(f"Target website data collected: {len(target_website_data)} fields")

# Display scraped data summary
print("\n" + "="*60)
print("WEBSITE DATA SUMMARY")
print("="*60)
print(f"Target URL: {target_url}")
print(f"Page Title: {target_website_data.get('title', 'N/A')}")
print(f"H1 Tags Found: {len(target_website_data.get('h1_tags', []))}")
print(f"Navigation Links: {len(target_website_data.get('navigation_links', []))}")
print(f"CTA Buttons: {len(target_website_data.get('cta_buttons', []))}")
print(f"Images with Alt Text: {len(target_website_data.get('images_alt_text', []))}")
if target_website_data.get('error'):
    print(f"Scraping Error: {target_website_data['error']}")
print("="*60)

# Example output to guide the model
# Example output to guide the model
example_output = """
Run a UX audit for this website: https://www.notion.com
SCREENSHOT: "notion.png given with this prompt"
Please evaluate using Nielsen’s heuristics and general UX copywriting standards.
UX Audit Report for Notion
Status: In Progress
Observations:
1. Location: Home page - Top nav/Hero section
Severity: High
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
Information architecture
UX copy
Observation:
The copy in the top strip, “All-in-one AI, right where you work” is vague and doesn’t provide enough context.
It competes with navigation due to visual prominence.
The link 'Try Notion AI free' leads to a repetitive CTA experience, causing confusion and pressure.
2. Location: Home page - Nav bar
Severity: High
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
Information architecture
UX copy
Observation:
The navigation label 'Notion' adds to CTA overload and makes the user experience feel rushed.
3. Location: Landing Page - Top nav
Severity: Medium
Heuristics Violated:
H7: Flexibility and efficiency of use
H12: Pleasurable and respectful interaction with the user
H8: Aesthetic and minimalist design
Information architecture
Observation:
The top navbar has disorganized options (AI, enterprises, pricing) that don’t align with product features.
AI appears both in the nav and as a CTA, reducing clarity and consistency.
4. Location: Landing Page - Hero section
Severity: Medium
Heuristics Violated:
H7: Flexibility and efficiency of use
H6: Recognition rather than recall
H8: Aesthetic and minimalist design
Observation:
The heading and subtext are vague, reduce the value proposition, and are difficult for users to relate to.
5. Location: Landing page - Hero section
Severity: Low
Heuristics Violated:
H5: Error prevention
H9: Help users recognize, diagnose, and recover from errors
Observation:
The banner image is uncommunicative, unrelated to the product, takes up excessive space, and adds usability friction.
6. Location: Landing page - AI meeting notes card groups
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H6: Recognition rather than recall
H12: Pleasurable and respectful interaction with the user
H8: Aesthetic and minimalist design
Observation:
The tooltip adds unnecessary friction.
The heading and subheading visually compete due to icon and badge placement.
Arrow placement is inconsistent across vertical and horizontal cards.
There are low contrast issues in the 'new' badge and Notion Mail card.
7. Location: Landing page - One space for everything
Severity: Low
Heuristics Violated:
H12: Pleasurable and respectful interaction with the user
H10: Help and documentation
H6: Recognition rather than recall
Observation:
Screenshots under 'one space for every team' don't align with navigation options and add cognitive load.
8. Location: Landing page - Customer stories
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H6: Recognition rather than recall
H12: Pleasurable and respectful interaction with the user
Information Architecture
Observation:
5 out of 7 testimonials have quoted subtexts, while 2 do not.
One testimonial is larger than the others.
Too much reading content distracts from the section's usefulness.
9. Location: Landing page - Find anything with one search
Severity: Low
Heuristics Violated:
H4: Consistency and standards
H12: Pleasurable and respectful interaction with the user
Observation:
The section appears visually bland due to low contrast and breaks the visual hierarchy.
10. Location: Landing page - Get started on Notion
Severity: Low
Heuristics Violated:
H6: Recognition rather than recall
H4: Consistency and standards
H5: Error prevention
Observation:
There are inconsistent CTA labels—one is Mac-specific vs. a generic 'download'.
The same color is used across different cards, breaking visual group distinction.
"""

# Enhanced instruction prompt with both image and URL analysis
def create_instruction_text(website_data, example_url, target_url, example_output):
    return f"""
You are an elite, meticulous UX evaluator, specializing in highly detailed, evidence-based heuristic audits of marketing SaaS landing pages. Your task is to perform a comprehensive heuristic evaluation using BOTH the static screenshot provided AND the supplementary website data extracted from the URL.

Your analysis must combine:
1. VISUAL EVIDENCE from the screenshot (primary source) - layout, design, visual hierarchy, spacing, colors, typography
2. STRUCTURAL EVIDENCE from the website's HTML, content, and metadata (secondary source) - semantic structure, accessibility features, content organization

This dual-source approach allows you to understand both how the website LOOKS (visual design) and how it's STRUCTURED (content architecture, accessibility features, technical implementation).

WEBSITE STRUCTURAL DATA AVAILABLE:
- Page title: {website_data.get('title', 'N/A')}
- Meta description: {website_data.get('meta_description', 'N/A')[:100]}{'...' if len(website_data.get('meta_description', '')) > 100 else ''}
- Main headings (H1): {', '.join(website_data.get('h1_tags', [])) if website_data.get('h1_tags') else 'None found'}
- Secondary headings (H2): {', '.join(website_data.get('h2_tags', [])[:3]) if website_data.get('h2_tags') else 'None found'}
- Navigation elements: {len(website_data.get('navigation_links', []))} links found
- CTA buttons detected: {len(website_data.get('cta_buttons', []))} buttons
- Form elements: {len(website_data.get('form_elements', []))} forms found
- Images with alt text: {len(website_data.get('images_alt_text', []))} images
- Sample visible text: {website_data.get('visible_text_sample', 'N/A')[:150]}{'...' if len(website_data.get('visible_text_sample', '')) > 150 else ''}

<system_constraints>
  You are operating in a constrained UX audit environment. You are an elite, meticulous UX evaluator whose role is to conduct high-fidelity heuristic evaluations of SaaS landing pages. All evaluations are based EXCLUSIVELY on the visual appearance of a **static screenshot**.

  HARD CONSTRAINTS:
  - You MUST NOT infer functionality, interactivity, or behavior (e.g., hover states, dynamic elements, hidden states, scroll behavior).
  - You MUST NOT generate assumptions about the product, purpose, or features beyond what is VISIBLE in the screenshot.
  - You MUST NOT hallucinate observations or extrapolate meanings not DIRECTLY visible from the screenshot content.
  - You MUST NOT offer solutions or redesign suggestions — focus SOLELY on observation and negative user impact.
  - You MAY reference heuristics (e.g., Nielsen’s) and UX principles ONLY if there is a direct and visually traceable violation.

  PERMITTED EVIDENCE SOURCES:
  - PRIMARY: The static screenshot provided by the user. All observations MUST be visually grounded in this source.
  - SUPPLEMENTARY: A provided URL may be crawled (static GET only) to inspect **only the first-load** HTML, CSS, and visible text DOM. JavaScript-rendered states, delayed content, A/B tests, auth walls, or animations must be treated as non-existent unless visible in the static screenshot.

  GOAL:
  Your goal is to identify clear, visible, significant UX or UI usability issues based on visual hierarchy, consistency, alignment, spacing, text clarity, accessibility, and other visually confirmable cues. You must mirror the granularity, tone, and specificity of expert human-led UX audits.

  PROCESS:
  - Scan from top to bottom of the screenshot in a systematic, section-by-section flow.
  - Within each section, inspect each individual element (text, button, image, badge, CTA, icon, card, etc.) and their group consistency.
  - Compare similar elements for visual inconsistencies.
  - For each visually evident issue, document:
    - Exact Location (e.g., “Hero Section – CTA Button”)
    - Violated UX Heuristics (e.g., Consistency and Standards, Visual Hierarchy)
    - Severity (Low, Medium, High)
    - Observation (specific, visual-evidence-based, 4–6 lines minimum, no assumptions)

  OUTPUT FORMAT:
  Your output MUST be a Markdown table with the following columns:
    * id: Unique identifier for the observation.
    * location: Provide the most granular and precise name for the specific UI element or visual region where the issue is undeniably visible. This must pinpoint a single, distinct visual component, or a specific part of a component.
        * Examples: "Header - 'Sign in' link", "Nav bar - 'Notion' link", "Hero Section - Main Heading Text", "Hero Section - Primary CTA Button ('Get Notion free' in header)", "Hero Section - Main Illustration/Image", "AI meeting notes card group - Vertical card 1 ('AI meeting notes')", "AI meeting notes card group - Horizontal card ('Notion Mail')", "AI meeting notes card group - 'New' badge on 'AI meeting notes' card", "Customer stories section - Testimonial Quote 1", "Footer - 'Product' links column", "Section 'Find anything with one search' - Heading", "Section 'get started on Notion' - 'Download for Mac' card".
    * heuristics_violated: An array of relevant UX principles/heuristics (from the comprehensive list above) that are violated by this specific UI element/issue.
    * severity: Assign "Low", "Medium", or "High" based on the visual impact and potential disruption to user experience.
    * observation: This must be a single, detailed narrative string. Within this string, you must:
    * Clearly describe the visually evident and specific issue related to the location.
    * Quote any exact visible text or alt-text relevant to this specific issue.
    * Mention if a CSS selector / XPath was found (otherwise state "N/A").
    * Crucially, explain how this specific visual characteristic or design choice, pertaining only to this exact UI element, could negatively impact the user's understanding, cognitive load, navigation, engagement, or overall experience. Be analytical and directly link the visual observation to the violated principles and user impact.
    * STRICTLY adhere to the visual evidence from the screenshot. DO NOT infer or invent.
    * DO NOT generalize or connect to other UI elements or sections if the issue is not directly and visually manifested by this specific element. If an identical visual issue appears on multiple distinct UI elements, create a separate observation for each instance, ensuring each location is unique to that element.

  RULES:
  - DO NOT combine issues across sections.
  - DO NOT reference functionality or flows not visible in the screenshot.
  - DO NOT provide design suggestions or enhancements.
  - DO NOT generalize observations — one row per specific issue.
  - DO NOT use vague language like “might confuse” — state the precise visual evidence and its user impact.

  Your behavior should emulate a senior human UX researcher performing a pixel-perfect visual audit.

</system_constraints>

[10-rule Markdown or bullet list]

✅ 10 Rigorous Heuristic Evaluation Rules (Markdown Table)
ID      Heuristic Rule (for Model Use)


H1      All observations must be based solely on what is visually and undeniably present in the screenshot. No assumptions about interactivity, hover states, or off-screen behavior are permitted.
Example:JSON:-
{{
  "heuristic": "H1",
  "location": "Top Navigation Bar",
  "observation": "There is a hamburger icon on the top-left, but no label or visible text indicating what it opens. Since the contents of the menu are not shown in the screenshot, no assumptions are made about its interactivity.",
  "severity": "Low"
}}

H2      Do not merge observations across sections or components. Each issue must be tied to one specific, visibly distinct element or immediate visual context.
Example:-
{{
  "heuristic": "H2",
  "location": "Feature Section > Card 2",
  "observation": "The icon in Card 2 is 5–6px lower than those in Card 1 and Card 3, breaking alignment within the card group. This issue is specific to Card 2 only.",
  "severity": "Medium"
}}

H3      Scrutinize micro-consistency within grouped elements. Actively compare spacing, alignment, icon positioning, or visual format across cards, CTAs, or testimonials to detect subtle inconsistencies.
Example:-
{{
  "heuristic": "H3",
  "location": "Testimonials Section",
  "observation": "Among three testimonial cards, Card 3 has an extra 10px of top margin on the quote text compared to the others, breaking visual rhythm.",
  "severity": "Medium"
}}

H4      Flag any imbalance or disruption in visual hierarchy. Identify when less important elements visually overpower or compete with key content like the hero heading or main CTA.
Example:-{{
  "heuristic": "H4",
  "location": "Hero Section",
  "observation": "The secondary CTA 'Learn More' uses a high-saturation blue and larger font size than the primary CTA 'Get Started', drawing disproportionate attention.",
  "severity": "High"
}}
H5      Evaluate imagery/animation for communication efficiency. Flag any visible visuals that take up significant screen space but fail to support product comprehension or context.
Example:-{{
  "heuristic": "H5",
  "location": "Hero Banner Background",
  "observation": "A large abstract video loop occupies nearly half the viewport without providing any context or supporting product explanation. It adds visual noise rather than clarity.",
  "severity": "Medium"
}}
H6      Assess clarity and specificity of all visible microcopy. Highlight vague or generic headings, subtexts, or CTAs that fail to communicate product value or purpose clearly.
Example:-{{
  "heuristic": "H6",
  "location": "Hero Section > Main Heading & Subtext",
  "observation": "The heading reads 'Unlock Your Potential' and the subtext says 'Do more with less', both of which are vague and do not explain the product, service, or offering in any concrete way.",
  "severity": "High"
}}
H7      Check for visual contrast and accessibility violations. Mark any text or badges that suffer from low contrast or weak visibility against background colors.
Example:-{{
  "heuristic": "H7",
  "location": "Pricing Section > Plan Card 3",
  "observation": "The white plan title text is placed over a light gray background with low contrast, making it difficult to read, especially for visually impaired users.",
  "severity": "High"
}}
H8      Detect visual rhythm or harmony breaks. Identify if changes in spacing, weight, or layout density create jarring transitions or disrupt the flow of the visual experience.
Example:-{{
  "heuristic": "H8",
  "location": "Feature Section > Card Layout",
  "observation": "The first two feature cards maintain consistent spacing, but the third card is placed 30px lower, creating a jagged, uneven row. This disrupts the visual flow.",
  "severity": "Medium"
}}
H9      Do not invent CSS selectors or metadata if not visible. If a selector or XPath isn't found directly via DOM crawl or visible inspection, state it as “N/A”.
Example:-{{
  "heuristic": "H9",
  "location": "CTA Button in Header",
  "observation": "The button shows the label 'Join Now', but no class name or selector is visible in the screenshot. Therefore, the selector is marked as 'N/A'.",
  "severity": "Low"
}}
H10     No recommendations. Only document precise visual problems and their impact. Do not suggest fixes or improvements; strictly record and analyze observed issues.{{
  "heuristic": "H10",
  "location": "Onboarding Modal",
  "observation": "The modal appears visually dense with four equal-weighted buttons and three text blocks stacked tightly. This creates a cluttered appearance with unclear hierarchy.",
  "severity": "Medium"
}}

Final Validation Check:
Before including any observation, ask: "Would fixing this issue genuinely improve user experience, or am I flagging a design choice preference?"

### Output Format:
{{
  "audit_report": {{
    "title": "<Title>",
    "observations": [
      {{
        "id": <int>,
        "location": "<Page location>",
        "severity": "<Low/Medium/High>",
        "section": "<Section name>",
        "heuristics_violated": ["<Heuristic 1>", "<Heuristic 2>"],
        "observation": "<Detailed observation>"
      }}
    ]
  }}
}}

### Example:
- Screenshot: [attached]
- URL: {example_url}
- Website Data: {json.dumps(example_website_data, indent=2) if 'example_website_data' in locals() else 'N/A'}
- Expected Output:
{example_output}

---

Now audit the following input:
- Screenshot: [attached below]
- URL: {target_url}
- Website Data: {json.dumps(website_data, indent=2)}
"""

# Create instruction text for both example and target
example_instruction_text = create_instruction_text(example_website_data, example_url, target_url, example_output)
target_instruction_text = create_instruction_text(target_website_data, example_url, target_url, example_output)

# Initialize Gemini model with configuration
llm = ChatGoogleGenerativeAI(model=MODEL_NAME, temperature=MODEL_TEMPERATURE)

def run_enhanced_ux_audit():
    """
    Main function to run the enhanced UX audit with both image and URL analysis
    """
    try:
        # Enhanced message sequence with both image and URL data
        messages = [
            SystemMessage(content="You are an expert UX auditor with access to both visual and structural website data."),
            HumanMessage(
                content=[
                    {"type": "text", "text": target_instruction_text},
                    {"type": "media", "data": example_image_bytes, "mime_type": "image/png"} if example_image_bytes else {"type": "text", "text": "Example image not available"},
                    {"type": "text", "text": f"Now audit this new input:\nScreenshot: [attached below]\nURL: {target_url}\nWebsite structural data has been provided above."},
                    {"type": "media", "data": target_image_bytes, "mime_type": "image/png"}
                ]
            )
        ]

        # Run the model
        print("Running enhanced UX audit with both visual and structural analysis...")
        print("This may take 30-60 seconds depending on the complexity...")

        response = llm.invoke(messages)

        # Output results
        print("\n" + "="*80)
        print("ENHANCED UX AUDIT RESULTS")
        print("="*80)
        print(f"Target URL: {target_url}")
        print(f"Page Title: {target_website_data.get('title', 'N/A')}")
        print(f"Analysis Date: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Model Used: {MODEL_NAME}")
        print("="*80)
        print(response.content)
        print("\n" + "="*80)
        print("AUDIT COMPLETE")
        print("="*80)

        return response.content

    except Exception as e:
        print(f"\nError during UX audit: {str(e)}")
        print("Please check your API key, image paths, and internet connection.")
        return None

# Run the enhanced UX audit
if __name__ == "__main__":
    print("="*80)
    print("ENHANCED UX AUDIT SCRIPT")
    print("="*80)
    print("This script analyzes websites using both visual screenshots and URL structure")
    print(f"Target URL: {target_url}")
    print(f"Model: {MODEL_NAME}")
    print("="*80)

    # Validate configuration
    print("\nValidating configuration...")
    config_issues = validate_configuration()

    if config_issues:
        print("Configuration issues found:")
        for issue in config_issues:
            print(f"  ❌ {issue}")

        # Check if we can continue
        critical_issues = [issue for issue in config_issues if "Target image not found" in issue or "GOOGLE_API_KEY" in issue]
        if critical_issues:
            print("\nCannot continue due to critical configuration issues.")
            exit(1)
        else:
            print("\nContinuing with warnings...")
    else:
        print("✅ Configuration validated successfully")

    # Run the audit
    print(f"\nStarting analysis of: {target_url}")
    result = run_enhanced_ux_audit()

    # Save results if successful
    if result:
        save_audit_results(result)
        print("\n✅ Enhanced UX audit completed successfully!")
        print("\nKey improvements in this enhanced version:")
        print("  • Dual-source analysis (visual + structural)")
        print("  • Enhanced accessibility evaluation")
        print("  • Content hierarchy assessment")
        print("  • Technical implementation review")
        print("  • Comprehensive error handling")
    else:
        print("\n❌ Audit failed. Please check the error messages above.")
